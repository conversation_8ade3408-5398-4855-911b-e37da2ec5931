"use strict";exports.id="dist_client_dev_noop-turbopack-hmr_js",exports.ids=["dist_client_dev_noop-turbopack-hmr_js"],exports.modules={"./dist/client/dev/noop-turbopack-hmr.js":/*!***********************************************!*\
  !*** ./dist/client/dev/noop-turbopack-hmr.js ***!
  \***********************************************/(module,exports1)=>{function connect(){}Object.defineProperty(exports1,"__esModule",{value:!0}),Object.defineProperty(exports1,"connect",{enumerable:!0,get:function(){return connect}}),("function"==typeof exports1.default||"object"==typeof exports1.default&&null!==exports1.default)&&void 0===exports1.default.__esModule&&(Object.defineProperty(exports1.default,"__esModule",{value:!0}),Object.assign(exports1.default,exports1),module.exports=exports1.default)}};
//# sourceMappingURL=dist_client_dev_noop-turbopack-hmr_js-turbo-experimental.runtime.dev.js.map